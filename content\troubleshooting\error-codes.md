---
title: Error Codes
description: Understanding and resolving error codes in Kantoku.
order: 2
---

# Error Codes Reference

![Error Codes](/figma-designs/instruction-12.png)

This reference guide helps you understand and resolve common error codes.

## Authentication Errors

### Error 401: Unauthorized
**Cause:** Invalid or expired credentials
**Solution:** 
- Log out and log back in
- Check your username and password
- Contact admin if account is locked

### Error 403: Forbidden
**Cause:** Insufficient permissions
**Solution:**
- Contact your administrator
- Verify your user role and permissions

## Connection Errors

### Error 500: Internal Server Error
**Cause:** Server-side issue
**Solution:**
- Wait a few minutes and try again
- Contact support if error persists

### Error 503: Service Unavailable
**Cause:** Server maintenance or overload
**Solution:**
- Check our status page
- Try again later
- Contact support for updates

For additional error codes not listed here, please contact our technical support team.
