import React from 'react';
import Navbar from '@/components/Navbar';
import Sidebar from '@/components/Sidebar';

export default function DocsLayout({
  children,
  params: {locale}
}: {
  children: React.ReactNode;
  params: {locale: string};
}) {
  return (
    <div className="relative min-h-screen bg-white">
      <Navbar locale={locale} />
      <Sidebar locale={locale} />
      <main className="ml-72 pt-16">
        <div className="p-8 lg:p-12 min-h-[calc(100vh-4rem)] bg-white">
          {children}
        </div>
      </main>
    </div>
  );
}
