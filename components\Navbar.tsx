import Link from 'next/link';

export default function Navbar() {
  return (
    <header className="bg-slate-800 text-white p-4 fixed top-0 left-0 right-0 z-50 h-16 flex items-center">
      <div className="container mx-auto flex justify-between items-center">
        <Link href="/" className="text-xl font-bold hover:text-blue-400 transition-colors">
          Kantoku Help
        </Link>
        {/* Future: Add search bar or other nav links here */}
        <nav>
          {/* Example link */}
          {/* <Link href="/about" className="hover:text-blue-400 transition-colors">About</Link> */}
        </nav>
      </div>
    </header>
  );
} 