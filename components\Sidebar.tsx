import Link from 'next/link';
import { getSortedArticlesData, ArticleData } from '@/lib/markdownProcessor';

interface GroupedArticles {
  [category: string]: ArticleData[];
}

export default function Sidebar() {
  const allArticles = getSortedArticlesData();

  const groupedArticles = allArticles.reduce<GroupedArticles>((acc, article) => {
    const category = article.category || 'General'; // Fallback for uncategorized articles
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(article);
    return acc;
  }, {});

  return (
    <aside className="w-72 bg-gray-50 p-4 border-r fixed top-16 left-0 h-[calc(100vh-4rem)] overflow-y-auto">
      {/* Adjusted top to be below navbar (h-16 = 4rem), and height to fill remaining screen */}
      <nav className="mt-4">
        {Object.entries(groupedArticles).map(([category, articles]) => (
          <div key={category} className="mb-6">
            <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-2 px-2">
              {category.replace(/-/g, ' ')}
            </h3>
            <ul>
              {articles.map((article) => (
                <li key={article.id}>
                  <Link
                    href={`/docs/${article.category}/${article.id}`}
                    className="block px-2 py-1.5 text-sm text-gray-700 hover:bg-gray-200 hover:text-gray-900 rounded-md transition-colors"
                  >
                    {article.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </nav>
    </aside>
  );
} 