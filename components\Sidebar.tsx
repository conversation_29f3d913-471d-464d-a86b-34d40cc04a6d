import Link from 'next/link';
import { getSortedArticlesData, ArticleData } from '@/lib/markdownProcessor';
import {useTranslations} from 'next-intl';

interface GroupedArticles {
  [category: string]: ArticleData[];
}

export default function Sidebar({locale}: {locale: string}) {
  const allArticles = getSortedArticlesData(locale);
  const t = useTranslations('sidebar');
  const tCategories = useTranslations('categories');

  const groupedArticles = allArticles.reduce<GroupedArticles>((acc, article) => {
    const category = article.category || 'General'; // Fallback for uncategorized articles
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(article);
    return acc;
  }, {});

  return (
    <aside className="w-72 bg-white border-r border-gray-200 fixed top-16 left-0 h-[calc(100vh-4rem)] overflow-y-auto shadow-sm">
      <div className="p-6">
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-2">{t('title')}</h2>
          <p className="text-sm text-gray-600">{t('subtitle')}</p>
        </div>

        <nav className="space-y-6">
          {Object.entries(groupedArticles).map(([category, articles]) => (
            <div key={category} className="space-y-2">
              <h3 className="text-sm font-semibold text-gray-900 uppercase tracking-wider px-3 py-2 bg-gray-50 rounded-md">
                {tCategories(category as any) || category.replace(/-/g, ' ')}
              </h3>
              <ul className="space-y-1">
                {articles.map((article) => (
                  <li key={article.id}>
                    <Link
                      href={`/${locale}/${article.category}/${article.id}`}
                      className="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded-md transition-colors border-l-2 border-transparent hover:border-blue-500"
                    >
                      {article.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </nav>
      </div>
    </aside>
  );
} 