import { getAllArticleIds, getArticleData, Article } from '@/lib/markdownProcessor';
import { notFound } from 'next/navigation';

interface PageProps {
  params: {
    category: string;
    slug: string;
  };
}

export async function generateStaticParams() {
  const paths = getAllArticleIds();
  return paths;
}

export async function generateMetadata({ params }: PageProps) {
  try {
    const article = await getArticleData(params.category, params.slug);
    return {
      title: `${article.title} - Kantoku Help Manual`,
      description: article.description || 'Kantoku help article',
    };
  } catch (error) {
    // If article not found, we can return default metadata or handle appropriately
    return {
      title: "Article Not Found - Kantoku Help Manual",
      description: "This help article could not be found.",
    };
  }
}

export default async function ArticlePage({ params }: PageProps) {
  let article: Article;
  try {
    article = await getArticleData(params.category, params.slug);
  } catch (error) {
    console.error(`Error fetching article: ${params.category}/${params.slug}`, error);
    notFound(); // This will render the nearest not-found.tsx or Next.js default 404 page
  }

  return (
    <article className="prose prose-sm sm:prose-base lg:prose-lg xl:prose-xl 2xl:prose-2xl max-w-none">
      <h1>{article.title}</h1>
      {article.description && <p className="lead">{article.description}</p>}
      <div dangerouslySetInnerHTML={{ __html: article.contentHtml }} />
      {/* TODO: Add navigation for Next/Previous article if desired */}
    </article>
  );
}

// Optional: Add a not-found.tsx in this directory or at the app level
// to customize the 404 page for missing articles. 