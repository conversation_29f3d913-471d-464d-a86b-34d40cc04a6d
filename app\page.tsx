import Link from 'next/link';

export default function Home() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-12 md:p-24 bg-gradient-to-br from-slate-900 to-slate-800 text-white">
      <div className="text-center max-w-2xl">
        <h1 className="text-5xl md:text-6xl font-bold mb-6">
          Welcome to the Kantoku Help Manual
        </h1>
        <p className="text-lg md:text-xl mb-8 text-slate-300">
          Your comprehensive guide to understanding and using Kantoku. Find answers, learn new features, and get the most out of our platform.
        </p>

        {/* Placeholder Search Bar */}
        <div className="mb-12">
          <input
            type="search"
            placeholder="Search documentation... (coming soon)"
            className="w-full max-w-md px-4 py-3 rounded-lg text-slate-800 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-shadow shadow-lg"
            disabled // Disabled until search is implemented
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Example link to documentation - adjust as needed */}
          <Link href="/docs/getting-started/introduction" className="block bg-slate-700 hover:bg-slate-600 p-6 rounded-lg transition-colors shadow-md">
            <h3 className="text-xl font-semibold mb-2">Getting Started</h3>
            <p className="text-slate-300">New to Kantoku? Begin your journey here.</p>
          </Link>

          <Link href="/docs/getting-started/introduction" className="block bg-slate-700 hover:bg-slate-600 p-6 rounded-lg transition-colors shadow-md">
            <h3 className="text-xl font-semibold mb-2">Explore Features</h3>
            <p className="text-slate-300">Discover the powerful features Kantoku offers.</p>
          </Link>
        </div>

        <p className="mt-12 text-sm text-slate-400">
          Browse all articles using the sidebar in our <Link href="/docs/getting-started/introduction" className="underline hover:text-blue-400">documentation section</Link>.
        </p>
      </div>
    </main>
  );
}
