const fs = require('fs');
const path = require('path');

const contentDirectory = path.join(process.cwd(), 'content');

console.log('Content directory:', contentDirectory);
console.log('Directory exists:', fs.existsSync(contentDirectory));

if (fs.existsSync(contentDirectory)) {
  const categories = fs.readdirSync(contentDirectory);
  console.log('Categories found:', categories);
  
  categories.forEach(category => {
    const categoryPath = path.join(contentDirectory, category);
    if (fs.statSync(categoryPath).isDirectory()) {
      const files = fs.readdirSync(categoryPath);
      console.log(`Files in ${category}:`, files);
    }
  });
}
