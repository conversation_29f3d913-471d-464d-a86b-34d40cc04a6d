import React from 'react';
import Navbar from '@/components/Navbar';
import Sidebar from '@/components/Sidebar';

export default function DocsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="relative min-h-screen bg-gray-100">
      <Navbar />
      <Sidebar />
      <main className="ml-72 pt-16">
        <div className="p-6 lg:p-8 min-h-[calc(100vh-4rem)]">
          {children}
        </div>
      </main>
    </div>
  );
} 