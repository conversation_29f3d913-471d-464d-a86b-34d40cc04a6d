{"mcpServers": {"Framelink Figma MCP": {"command": "cmd", "args": ["/c", "npx", "-y", "figma-developer-mcp", "--figma-api-key=*********************************************", "--st<PERSON>"]}, "github": {"command": "docker", "args": ["run", "-i", "--rm", "-e", "GITHUB_PERSONAL_ACCESS_TOKEN", "mcp/github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}}}}